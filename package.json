{"name": "ecommerce-platform", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev:frontend": "pnpm --filter frontend dev", "dev:backend": "pnpm --filter backend dev", "dev:admin": "pnpm --filter admin dev", "build": "pnpm --recursive build", "test": "pnpm --recursive test", "lint": "pnpm --recursive lint", "format": "prettier --write ."}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0", "husky": "^8.0.0"}}