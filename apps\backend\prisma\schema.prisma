// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  role      String   @default("CUSTOMER") // CUSTOMER, ADMIN, SUPER_ADMIN
  isActive  Boolean  @default(true)
  profile   String?  // JSON string
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  orders    Order[]
  reviews   Review[]
  cartItems CartItem[]

  @@map("users")
}

model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  imageUrl    String?
  parentId    String?
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  products    Product[]
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  @@map("categories")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  description String?
  price       Float
  stock       Int      @default(0)
  imageUrls   String?  // JSON string for array of image URLs
  categoryId  String
  category    Category @relation(fields: [categoryId], references: [id])
  status      String   @default("ACTIVE") // ACTIVE, INACTIVE, OUT_OF_STOCK
  attributes  String?  // JSON string for product specifications
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  orderItems OrderItem[]
  reviews    Review[]
  cartItems  CartItem[]

  @@map("products")
}

model Order {
  id          String      @id @default(cuid())
  userId      String
  user        User        @relation(fields: [userId], references: [id])
  status      String      @default("PENDING") // PENDING, CONFIRMED, PROCESSING, SHIPPED, DELIVERED, CANCELLED, REFUNDED
  totalAmount Float
  shippingAddress String  // JSON string
  paymentMethod   String
  paymentStatus   String  @default("PENDING") // PENDING, PAID, FAILED, REFUNDED
  notes       String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  orderItems OrderItem[]
  coupons    OrderCoupon[]
  payments   Payment[]

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  order     Order   @relation(fields: [orderId], references: [id])
  productId String
  product   Product @relation(fields: [productId], references: [id])
  quantity  Int
  price     Float   // Price at time of order
  createdAt DateTime @default(now())

  @@map("order_items")
}

model Review {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  productId String
  product   Product  @relation(fields: [productId], references: [id])
  rating    Int      // 1-5 stars
  comment   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, productId])
  @@map("reviews")
}

model CartItem {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  productId String
  product   Product  @relation(fields: [productId], references: [id])
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, productId])
  @@map("cart_items")
}

model Coupon {
  id          String     @id @default(cuid())
  code        String     @unique
  name        String
  description String?
  type        String     // FIXED_AMOUNT, PERCENTAGE, FREE_SHIPPING
  value       Float      // Discount amount or percentage
  minAmount   Float?     // Minimum order amount
  maxDiscount Float?     // Maximum discount amount
  startDate   DateTime
  endDate     DateTime
  usageLimit  Int?       // Total usage limit
  usedCount   Int        @default(0)
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // Relations
  orders OrderCoupon[]

  @@map("coupons")
}

model OrderCoupon {
  id       String @id @default(cuid())
  orderId  String
  order    Order  @relation(fields: [orderId], references: [id])
  couponId String
  coupon   Coupon @relation(fields: [couponId], references: [id])
  discount Float  // Actual discount applied

  @@unique([orderId, couponId])
  @@map("order_coupons")
}

model Payment {
  id            String   @id @default(cuid())
  orderId       String
  order         Order    @relation(fields: [orderId], references: [id])
  amount        Float    // Payment amount (negative for refunds)
  method        String   // alipay, wechat, credit_card, bank_card, refund
  status        String   // SUCCESS, FAILED, PENDING
  transactionId String?  // External transaction ID
  failureReason String?  // Reason for failure
  processedAt   DateTime @default(now())
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("payments")
}

// Enums are replaced with String fields with comments indicating valid values
// UserRole: CUSTOMER, ADMIN, SUPER_ADMIN
// ProductStatus: ACTIVE, INACTIVE, OUT_OF_STOCK
// OrderStatus: PENDING, CONFIRMED, PROCESSING, SHIPPED, DELIVERED, CANCELLED, REFUNDED
// PaymentStatus: PENDING, PAID, FAILED, REFUNDED
// CouponType: FIXED_AMOUNT, PERCENTAGE, FREE_SHIPPING
