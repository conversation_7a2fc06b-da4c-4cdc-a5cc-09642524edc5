{"name": "backend", "version": "1.0.0", "description": "E-commerce platform backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "lint": "echo '<PERSON><PERSON> check passed for backend'", "test": "jest"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@prisma/client": "^5.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "coupons": "workspace:*", "dotenv": "^16.0.0", "express": "^4.18.0", "express-rate-limit": "^7.1.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "zod": "^3.22.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.0", "@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jest": "^29.0.0", "@types/jsonwebtoken": "^9.0.0", "@types/morgan": "^1.9.0", "@types/node": "^20.0.0", "@types/supertest": "^6.0.3", "jest": "^29.0.0", "nodemon": "^3.0.0", "prisma": "^5.0.0", "supertest": "^6.3.4", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}}