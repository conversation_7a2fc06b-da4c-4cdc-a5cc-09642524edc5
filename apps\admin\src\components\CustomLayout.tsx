import React from 'react';
import {
  Layout,
  AppBar,
  useSidebarState,
  CheckForApplicationUpdate,
  Error,
  Loading,
} from 'react-admin';
import CustomSidebar from './CustomSidebar';
import { APP_CONFIG, ENV_UTILS } from '../config/env';

/**
 * 自定义应用栏
 */
const CustomAppBar = () => {
  const [open] = useSidebarState();

  return (
    <AppBar
      style={{
        backgroundColor: '#fff',
        color: '#333',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        borderBottom: '1px solid #f0f0f0',
        marginLeft: open ? 240 : 64,
        width: `calc(100% - ${open ? 240 : 64}px)`,
        transition: 'all 0.2s ease-in-out',
      }}
    >
      <div
        style={{
          flex: 1,
          textAlign: 'center',
          fontSize: '1.2rem',
          fontWeight: 'bold',
          color: '#1976d2',
          padding: '0 24px',
        }}
      >
        🛒 {APP_CONFIG.TITLE}
        {ENV_UTILS.isDevelopment() && (
          <span style={{ fontSize: '0.8rem', marginLeft: '8px', opacity: 0.7 }}>
            (开发环境)
          </span>
        )}
      </div>
    </AppBar>
  );
};

/**
 * 简单的错误边界组件
 */
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          style={{
            padding: '20px',
            textAlign: 'center',
            backgroundColor: '#fff5f5',
            border: '1px solid #fed7d7',
            borderRadius: '8px',
            margin: '20px',
          }}
        >
          <h2 style={{ color: '#e53e3e', marginBottom: '16px' }}>出现错误</h2>
          <p style={{ color: '#666', marginBottom: '16px' }}>
            {this.state.error?.message || '应用程序遇到了一个错误'}
          </p>
          <button
            onClick={() => window.location.reload()}
            style={{
              backgroundColor: '#1976d2',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '4px',
              cursor: 'pointer',
            }}
          >
            重新加载
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * 自定义布局组件
 */
export const CustomLayout = (props: any) => {
  const [open] = useSidebarState();

  return (
    <div style={{ display: 'flex', height: '100vh', backgroundColor: '#f5f5f5' }}>
      {/* 侧边栏 */}
      <CustomSidebar />

      {/* 主内容区域 */}
      <div
        style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          transition: 'margin-left 0.2s ease-in-out',
          overflow: 'hidden',
        }}
      >
        {/* 顶部应用栏 */}
        <CustomAppBar />

        {/* 内容区域 */}
        <div
          style={{
            flex: 1,
            overflow: 'auto',
            padding: '24px',
            backgroundColor: '#f5f5f5',
          }}
        >
          <ErrorBoundary>
            <React.Suspense fallback={<Loading />}>
              {props.children}
            </React.Suspense>
          </ErrorBoundary>
        </div>
      </div>

      {/* 应用更新检查 */}
      <CheckForApplicationUpdate />
    </div>
  );
};

export default CustomLayout;
