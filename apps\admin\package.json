{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "echo '<PERSON><PERSON> check passed for admin'"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.25.3", "axios": "^1.6.0", "dayjs": "^1.11.13", "ra-data-json-server": "^4.16.0", "react": "^18.2.0", "react-admin": "^4.16.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "typescript": "^5.0.2", "vite": "^4.4.5"}}