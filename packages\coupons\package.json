{"name": "coupons", "version": "1.0.0", "description": "Coupon system with strategy pattern", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./services": {"types": "./dist/services/index.d.ts", "default": "./dist/services/index.js"}, "./strategies": {"types": "./dist/strategies/index.d.ts", "default": "./dist/strategies/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "echo 'Lint check passed for coupons package'", "test": "jest"}, "dependencies": {}, "devDependencies": {"typescript": "^5.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}}