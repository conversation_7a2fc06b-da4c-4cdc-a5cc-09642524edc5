# 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# ================================
# API配置
# ================================
# 后端API基础URL
VITE_API_URL=http://localhost:3000/api

# API请求超时时间（毫秒）
VITE_API_TIMEOUT=10000

# API请求重试次数
VITE_API_RETRY_COUNT=3

# ================================
# 应用配置
# ================================
# 应用标题
VITE_APP_TITLE=电商平台管理后台

# 应用版本
VITE_APP_VERSION=1.0.0

# 应用环境
VITE_APP_ENV=development

# 开发服务器端口
VITE_DEV_PORT=5173

# ================================
# 调试配置
# ================================
# 是否启用调试模式 (true/false)
VITE_DEBUG=true

# 日志级别 (debug/info/warn/error)
VITE_LOG_LEVEL=debug

# 是否显示性能信息 (true/false)
VITE_SHOW_PERFORMANCE=false

# ================================
# 功能开关
# ================================
# 是否启用暗色主题 (true/false)
VITE_ENABLE_DARK_THEME=true

# 是否启用国际化 (true/false)
VITE_ENABLE_I18N=false

# 是否启用数据导出 (true/false)
VITE_ENABLE_EXPORT=true

# 是否启用实时通知 (true/false)
VITE_ENABLE_REALTIME=false
